
using Common.NetMsg;
using DashGame;
using Google.Protobuf.Collections;
using Proto.LogicData;
using System.Collections.Generic;

public class SetPanel : Panel
{
    private BaseNetActivityInfo activityInfo = null;

    public SetPanel()
    {
        packName = "Setting";
        compName = "SettingPanel";
        modal = true;
    }

    protected override void DoInitialize()
    {
        var btnVibration = contentPane.GetChild("btnVibration").asButton;
        var btnBroad = contentPane.GetChild("btnBroad").asButton;
        // var btnEffects = contentPane.GetChild("btnEffects").asButton;
        //var btnRocker = contentPane.GetChild("btnRocker").asButton;
        //var lblPlayerId = contentPane.GetChild("lblPlayerId").asTextField;
        var lblVersionNumber = contentPane.GetChild("lblVersionNumber").asTextField;
        // var comboPicture = contentPane.GetChild("comboPicture").asComboBox;
        var sliderSound = contentPane.GetChild("sliderSound").asSlider;
        var sliderMusic = contentPane.GetChild("sliderMusic").asSlider;

        sliderSound.onChanged.Add(() => {
            StorageMgr.SetEffectVolume((float)sliderSound.value * 0.01f);
            SoundManager.SetEffectVolume((float)sliderSound.value * 0.01f);
        });

        sliderMusic.onChanged.Add(() => {
            StorageMgr.SetBgVolume((float)sliderMusic.value * 0.01f);
            SoundManager.SetBgmVolume((float)sliderMusic.value * 0.01f);
        });
        sliderSound.value = StorageMgr.GetEffectVolume() * 100f;
        sliderMusic.value = StorageMgr.GetBgVolume() * 100f;
        
        btnVibration.onChanged.Set(() =>
        {
            Session.Vibration = btnVibration.selected;
            StorageMgr.SetVibration(btnVibration.selected);
        });
        btnVibration.selected = Session.Vibration;

        btnBroad.selected = StorageMgr.Broad;
        btnBroad.onClick.Set(() =>
        {
            StorageMgr.Broad = btnBroad.selected;
        });
        lblVersionNumber.text = $"{AssetBundleManager.GetVersion()}({GameConfig.GetBuildVer()})";

        var btnGiftCode = contentPane.GetChild("btnGiftCode").asButton;
        btnGiftCode.visible = FunStatusUtil.GetInstance().GetFunStatus((int)FunType.GiftCode);
        this.Request();
    }

    private void Request()
    {
        NetMgr.RequestActivitiesInfo((int)ActivityDisplayUiEnum.Runner);
    }


    protected override void OnMessageRecieve(NetMessage msg)
    {
        var cmd = msg.GetCmd();
        var data = msg.data;
        switch (cmd)
        {
            case NetMsg.S2CRequestActivitiesInfoResult:
                var tempDataInfo = msg.Parse<RequestActivitiesInfoResult>();
                if (tempDataInfo.DisplayUi == (int)ActivityDisplayUiEnum.Runner && tempDataInfo.ActivityList.Count > 0)
                {
                    activityInfo = tempDataInfo.ActivityList[0];
                }

                break;
            case NetMsg.S2CRequestActivityGateInfo:
                var battleInfoResult = msg.Parse<RequestActivityBattleInfoResult>();
                if (battleInfoResult.Result == 0)
                {
                    Request();
                }
                else
                {
                    TipMgr.ShowTip(LangUtil.GetErrorCodeMsg(battleInfoResult.Result));
                }
                break;


        }
    }

    protected override void OnMouseClick(string targetName)
    {
        switch (targetName)
        {
            case "btnClose":
                this.Hide();
                break;
            case "btnReplace":
                //NotifyMgr.Event(NotifyNames.CheckChangeTable);

                break;
            case "btnSignOut":
                this.Hide();
                Platform.GetInstance().Logout();
                break;
            case "btnReplay":
                this.Hide();
                //TipMgr.ShowMsgBox(LangUtil.GetText("txtArenaGiveUp"), TipsBox.YES | TipsBox.NO, (value) => {
                //    if (value == TipsBox.YES)
                //    {
                //        NetMgr.GiveUpArena();
                //    }
                //});
                break;
            case "btnDeleteAccount":
                //TipMgr.ShowDeleteAccountMsgBox("", TipsBox.YES | TipsBox.NO, (value) => {
                //    if (value == TipsBox.YES)
                //    {
                //        NetMgr.PlayerDelAccount();
                //    }
                //});
                break;
            case "btnGiftCode":
                Panel.Create((GiftCodePanel panel) =>
                {
                });
                break;
            case "btnUserAgreement":
                Create<UserAgreementPanel>((panel) =>
                {
                    panel.SetData(1);
                });
                break;
            case "btnPrivacyPolicy":
                Create<UserAgreementPanel>((panel) =>
                {
                    panel.SetData(2);
                });
                break;
            case "btnOfficial":
                Create<OfficialPanel>();
                break;
            case "btnMemberCenter":
                Platform.GetInstance().ShowMemberCenter();
                break;
            case "btnFeedback":
                Platform.GetInstance().OpenCustomerServiceConversation();
                break;
            case "btnWildRun":
                if (activityInfo != null)
                {
                    Create<GateListPanel>((panel) =>
                    {
                        panel.SetData(activityInfo.Id);
                    });
                }                
                break;
        }
    }
}
