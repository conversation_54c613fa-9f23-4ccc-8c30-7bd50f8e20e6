<dependencies>
  <packages>
    <package>androidx.recyclerview:recyclerview:1.2.1</package>
    <package>com.facebook.android:audience-network-sdk:6.20.0</package>
    <package>com.google.android.gms:play-services-ads:24.3.0</package>
    <package>com.google.android.gms:play-services-ads-identifier:18.1.0</package>
    <package>com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.61</package>
    <package>com.pangle.global:pag-sdk:7.5.0.2</package>
    <package>com.unity3d.ads:unity-ads:4.14.2</package>
    <package>com.unity3d.ads-mediation:admob-adapter:4.3.51</package>
    <package>com.unity3d.ads-mediation:adquality-sdk:7.24.3</package>
    <package>com.unity3d.ads-mediation:facebook-adapter:4.3.50</package>
    <package>com.unity3d.ads-mediation:mediation-sdk:8.9.1</package>
    <package>com.unity3d.ads-mediation:mintegral-adapter:4.3.38</package>
    <package>com.unity3d.ads-mediation:pangle-adapter:4.3.49</package>
    <package>com.unity3d.ads-mediation:unityads-adapter:4.3.54</package>
    <package>com.unity3d.ads-mediation:vungle-adapter:4.3.31</package>
    <package>com.vungle:vungle-ads:7.5.1</package>
  </packages>
  <files />
  <settings>
    <setting name="androidAbis" value="arm64-v8a,armeabi-v7a" />
    <setting name="bundleId" value="com.touchbox.sdz" />
    <setting name="explodeAars" value="True" />
    <setting name="gradleBuildEnabled" value="True" />
    <setting name="gradleTemplateEnabled" value="True" />
    <setting name="installAndroidPackages" value="True" />
    <setting name="packageDir" value="Assets/Plugins/Android" />
    <setting name="patchAndroidManifest" value="True" />
    <setting name="patchMainTemplateGradle" value="True" />
    <setting name="projectExportEnabled" value="True" />
    <setting name="useJetifier" value="True" />
  </settings>
</dependencies>