// Android Resolver Repos Start
([rootProject] + (rootProject.subprojects as List)).each {
    ext {
        it.setProperty("android.useAndroidX", true)
        it.setProperty("android.enableJetifier", true)
    }
}
([rootProject] + (rootProject.subprojects as List)).each { project ->
    project.repositories {
        def unityProjectPath = $/file:///**DIR_UNITYPROJECT**/$.replace("\\", "/")
        maven {
            url "https://maven.google.com"
        }
        maven {
            url "https://maven.google.com/" // Assets/LevelPlay/Editor/IronSourceSDKDependencies.xml:12, Assets/LevelPlay/Editor/ISAdMobAdapterDependencies.xml:8, Assets/LevelPlay/Editor/ISFacebookAdapterDependencies.xml:8, Assets/LevelPlay/Editor/ISMintegralAdapterDependencies.xml:15, Assets/LevelPlay/Editor/ISUnityAdsAdapterDependencies.xml:12
        }
        maven {
            url "https://dl-maven-android.mintegral.com/repository/mbridge_android_sdk_oversea/" // Assets/LevelPlay/Editor/ISMintegralAdapterDependencies.xml:8
        }
        maven {
            url "https://artifact.bytedance.com/repository/pangle/" // Assets/LevelPlay/Editor/ISPangleAdapterDependencies.xml:12
        }
        mavenLocal()
        jcenter()
        mavenCentral()
    }
}
// Android Resolver Repos End
apply plugin: 'com.android.library'
**APPLY_PLUGINS**

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
// Android Resolver Dependencies Start
    implementation 'androidx.recyclerview:recyclerview:1.2.1' // Assets/LevelPlay/Editor/ISMintegralAdapterDependencies.xml:15
    implementation 'com.facebook.android:audience-network-sdk:6.20.0' // Assets/LevelPlay/Editor/ISFacebookAdapterDependencies.xml:8
    implementation 'com.google.android.gms:play-services-ads:24.3.0' // Assets/LevelPlay/Editor/ISAdMobAdapterDependencies.xml:8
    implementation 'com.google.android.gms:play-services-ads-identifier:18.1.0' // Assets/LevelPlay/Editor/IronSourceSDKDependencies.xml:12
    implementation 'com.mbridge.msdk.oversea:mbridge_android_sdk:16.9.61' // Assets/LevelPlay/Editor/ISMintegralAdapterDependencies.xml:8
    implementation 'com.pangle.global:pag-sdk:7.5.0.2' // Assets/LevelPlay/Editor/ISPangleAdapterDependencies.xml:12
    implementation 'com.unity3d.ads:unity-ads:4.14.2' // Assets/LevelPlay/Editor/ISUnityAdsAdapterDependencies.xml:12
    implementation 'com.unity3d.ads-mediation:admob-adapter:4.3.51' // Assets/LevelPlay/Editor/ISAdMobAdapterDependencies.xml:12
    implementation 'com.unity3d.ads-mediation:adquality-sdk:7.24.3' // Assets/LevelPlay/Editor/IronSourceAdQualityDependencies.xml:6
    implementation 'com.unity3d.ads-mediation:facebook-adapter:4.3.50' // Assets/LevelPlay/Editor/ISFacebookAdapterDependencies.xml:12
    implementation 'com.unity3d.ads-mediation:mediation-sdk:8.9.1' // Assets/LevelPlay/Editor/IronSourceSDKDependencies.xml:5
    implementation 'com.unity3d.ads-mediation:mintegral-adapter:4.3.38' // Assets/LevelPlay/Editor/ISMintegralAdapterDependencies.xml:19
    implementation 'com.unity3d.ads-mediation:pangle-adapter:4.3.49' // Assets/LevelPlay/Editor/ISPangleAdapterDependencies.xml:5
    implementation 'com.unity3d.ads-mediation:unityads-adapter:4.3.54' // Assets/LevelPlay/Editor/ISUnityAdsAdapterDependencies.xml:5
    implementation 'com.unity3d.ads-mediation:vungle-adapter:4.3.31' // Assets/LevelPlay/Editor/ISVungleAdapterDependencies.xml:9
    implementation 'com.vungle:vungle-ads:7.5.1' // Assets/LevelPlay/Editor/ISVungleAdapterDependencies.xml:5
// Android Resolver Dependencies End
    implementation 'com.gd:gdsdk-touchboxs:3.8.3'
    
    // 替换旧的（review引用） com.google.android.play:core:1.10.3 为新的分离库（google ads引用）以解决依赖冲突
    implementation 'com.google.android.play:core-common:2.0.2'
    implementation 'com.google.android.play:review:2.0.1'
    //其实没用，只是为了替换后不会引入core，不然还得手动注释掉com.google.android.play:core:1.10.3

    
**DEPS**}

// Android Resolver Exclusions Start
android {
  packagingOptions {
      exclude ('/lib/armeabi/*' + '*')
      exclude ('/lib/mips/*' + '*')
      exclude ('/lib/mips64/*' + '*')
      exclude ('/lib/x86/*' + '*')
      exclude ('/lib/x86_64/*' + '*')
  }
}
// Android Resolver Exclusions End
android {
    compileSdkVersion **APIVERSION**
    buildToolsVersion '**BUILDTOOLS**'

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    defaultConfig {
        minSdkVersion **MINSDKVERSION**
        targetSdkVersion **TARGETSDKVERSION**
        ndk {
            abiFilters **ABIFILTERS**
        }
        versionCode **VERSIONCODE**
        versionName '**VERSIONNAME**'
        consumerProguardFiles 'proguard-unity.txt'**USER_PROGUARD**
    }

    lintOptions {
        abortOnError false
    }

    aaptOptions {
        noCompress = **BUILTIN_NOCOMPRESS** + unityStreamingAssets.tokenize(', ')
        ignoreAssetsPattern = "!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"
    }**PACKAGING_OPTIONS**
}**REPOSITORIES**
**IL_CPP_BUILD_SETUP**
**SOURCE_BUILD_SETUP**
**EXTERNAL_SOURCES**
