using System;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;

namespace WGame.Editor
{
    /// <summary>
    /// 版本号项目类
    /// </summary>
    [System.Serializable]
    public class VersionItem
    {
        [SerializeField]
        public string version;

        [SerializeField]
        public bool isSelected;

        public VersionItem()
        {
            version = "";
            isSelected = true;
        }

        public VersionItem(string version, bool isSelected = true)
        {
            this.version = version;
            this.isSelected = isSelected;
        }
    }

    /// <summary>
    /// 构建配置数据类
    /// </summary>
    [System.Serializable]
    public class BuildConfigData
    {
        [SerializeField]
        public List<VersionItem> versionItems = new List<VersionItem>();
        
        [SerializeField]
        public int resVersion = 26;
    }

    /// <summary>
    /// 构建配置单例管理器
    /// 提供统一的配置访问接口，支持自动保存和加载
    /// </summary>
    public class BuildConfig
    {
        private static BuildConfig _instance;
        private static readonly object _lock = new object();
        
        private const string BUILD_CONFIG_PATH = "Assets/_MyGame/Editor/BuildConfig.json";
        private BuildConfigData _data;
        private bool _isDirty = false;

        /// <summary>
        /// 获取单例实例
        /// </summary>
        public static BuildConfig Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new BuildConfig();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// 私有构造函数
        /// </summary>
        private BuildConfig()
        {
            LoadConfig();
        }

        /// <summary>
        /// 版本项目列表
        /// </summary>
        public List<VersionItem> VersionItems
        {
            get => _data.versionItems;
            set
            {
                _data.versionItems = value;
                MarkDirty();
            }
        }

        /// <summary>
        /// 热更版本号
        /// </summary>
        public int ResVersion
        {
            get => _data.resVersion;
            set
            {
                if (_data.resVersion != value)
                {
                    _data.resVersion = value;
                    MarkDirty();
                }
            }
        }

        /// <summary>
        /// 标记为需要保存
        /// </summary>
        private void MarkDirty()
        {
            _isDirty = true;
            // 延迟保存，避免频繁IO操作
            EditorApplication.delayCall += SaveIfDirty;
        }

        /// <summary>
        /// 如果有修改则保存
        /// </summary>
        private void SaveIfDirty()
        {
            if (_isDirty)
            {
                SaveConfig();
                _isDirty = false;
            }
        }

        /// <summary>
        /// 立即保存配置
        /// </summary>
        public void SaveConfig()
        {
            try
            {
                // 确保目录存在
                string directory = Path.GetDirectoryName(BUILD_CONFIG_PATH);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // 序列化为JSON
                string json = JsonUtility.ToJson(_data, true);
                File.WriteAllText(BUILD_CONFIG_PATH, json);
                
                Debug.Log($"✅ [BuildConfig] 配置已保存: {BUILD_CONFIG_PATH}");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ [BuildConfig] 保存配置失败: {e.Message}");
            }
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        private void LoadConfig()
        {
            try
            {
                if (File.Exists(BUILD_CONFIG_PATH))
                {
                    string json = File.ReadAllText(BUILD_CONFIG_PATH);
                    _data = JsonUtility.FromJson<BuildConfigData>(json);
                    
                    if (_data != null && _data.versionItems != null)
                    {
                        Debug.Log($"✅ [BuildConfig] 配置已加载: ResVersion={_data.resVersion}, VersionItems={_data.versionItems.Count}");
                        return;
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogWarning($"⚠️ [BuildConfig] 加载配置失败: {e.Message}");
            }

            // 如果加载失败或文件不存在，创建默认配置
            CreateDefaultConfig();
        }

        /// <summary>
        /// 创建默认配置
        /// </summary>
        private void CreateDefaultConfig()
        {
            _data = new BuildConfigData();
            _data.versionItems.Add(new VersionItem("1.0.0", true));
            _data.versionItems.Add(new VersionItem("1.0.1", true));
            _data.versionItems.Add(new VersionItem("1.1.0", false));
            _data.resVersion = 1;
            
            SaveConfig();
            Debug.Log("📋 [BuildConfig] 创建默认配置");
        }

        /// <summary>
        /// 重新加载配置
        /// </summary>
        public void ReloadConfig()
        {
            LoadConfig();
        }

        /// <summary>
        /// 添加版本项目
        /// </summary>
        public void AddVersionItem(string version, bool isSelected = true)
        {
            if (string.IsNullOrEmpty(version))
                return;

            // 检查是否已存在
            foreach (var item in _data.versionItems)
            {
                if (item.version == version)
                {
                    Debug.LogWarning($"版本号 {version} 已存在");
                    return;
                }
            }

            _data.versionItems.Add(new VersionItem(version, isSelected));
            MarkDirty();
        }

        /// <summary>
        /// 检查版本号是否已存在
        /// </summary>
        public bool VersionExists(string version)
        {
            foreach (var item in _data.versionItems)
            {
                if (item.version == version)
                    return true;
            }
            return false;
        }

        /// <summary>
        /// 设置全选/取消全选
        /// </summary>
        public void SetAllSelected(bool selected)
        {
            foreach (var item in _data.versionItems)
            {
                item.isSelected = selected;
            }
            MarkDirty();
        }

        /// <summary>
        /// 清空版本列表
        /// </summary>
        public void ClearVersions()
        {
            _data.versionItems.Clear();
            MarkDirty();
        }

        /// <summary>
        /// 获取选中的版本号列表
        /// </summary>
        public string[] GetSelectedVersions()
        {
            var selectedVersions = new List<string>();
            foreach (var item in _data.versionItems)
            {
                if (item.isSelected)
                {
                    selectedVersions.Add(item.version);
                }
            }
            return selectedVersions.ToArray();
        }

        /// <summary>
        /// 获取选中版本的数量
        /// </summary>
        public int GetSelectedCount()
        {
            int count = 0;
            foreach (var item in _data.versionItems)
            {
                if (item.isSelected)
                    count++;
            }
            return count;
        }
    }
}
