using System;
using System.IO;
using UnityEditor;
using UnityEngine;

namespace WGame.Editor
{
    /// <summary>
    /// 版本编辑器工具
    /// </summary>
    public static class VersionEditor
    {
        private const string VERSION_FILE_PATH = "Assets/_MyGame/Resources/version.json";

        /// <summary>
        /// 更新版本信息
        /// </summary>
        public static void UpdateVersion(string version, int versionCode, string platformId = null, string assetDirectory = null)
        {
            try
            {
                // 如果没有指定platformId，使用当前的或默认值
                if (string.IsNullOrEmpty(platformId))
                {
                    var currentData = VersionManager.GetVersionData();
                    platformId = currentData?.platformId ?? "google";
                }

                // 如果没有指定assetDirectory，使用当前的或默认值
                if (string.IsNullOrEmpty(assetDirectory))
                {
                    var currentData = VersionManager.GetVersionData();
                    assetDirectory = currentData?.assetDirectory ?? "1.0.0";
                }

                // 创建新的版本数据
                VersionData versionData = new VersionData
                {
                    version = version,
                    versionCode = versionCode,
                    buildNumber = DateTime.Now.ToString("yyyyMMddHHmmss"),
                    lastUpdateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    platformId = platformId,
                    assetDirectory = assetDirectory
                };

                // 保存到JSON文件
                SaveVersionToFile(versionData);

                // 更新PlayerSettings
                UpdatePlayerSettings(versionData);

                // 重新加载缓存
                VersionManager.ReloadVersionData();

                Debug.Log($"✅ [VersionEditor] 版本已更新: v{version}({versionCode}), 平台ID: {platformId}, 资源目录: {assetDirectory}");
            }
            catch (Exception e)
            {
                Debug.LogError($"❌ [VersionEditor] 更新版本失败: {e.Message}");
            }
        }

        /// <summary>
        /// 保存版本数据到JSON文件
        /// </summary>
        private static void SaveVersionToFile(VersionData versionData)
        {
            // 确保目录存在
            string directory = Path.GetDirectoryName(VERSION_FILE_PATH);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // 转换为JSON并保存
            string json = JsonUtility.ToJson(versionData, true);
            File.WriteAllText(VERSION_FILE_PATH, json);

            // 刷新AssetDatabase
            AssetDatabase.Refresh();

            Debug.Log($"📝 [VersionEditor] 版本文件已保存: {VERSION_FILE_PATH}");
        }

        /// <summary>
        /// 更新PlayerSettings
        /// </summary>
        private static void UpdatePlayerSettings(VersionData versionData)
        {
            PlayerSettings.bundleVersion = versionData.version;

#if UNITY_ANDROID
            PlayerSettings.Android.bundleVersionCode = versionData.versionCode;
#elif UNITY_IOS
            PlayerSettings.iOS.buildNumber = versionData.versionCode.ToString();
#endif

            Debug.Log($"✅ [VersionEditor] PlayerSettings已更新");
        }

        /// <summary>
        /// 获取当前版本信息
        /// </summary>
        public static VersionData GetCurrentVersion()
        {
            return VersionManager.GetVersionData();
        }

        /// <summary>
        /// 创建默认版本文件
        /// </summary>
        // [MenuItem("Build/Create Default Version File", priority = 5)]
        // public static void CreateDefaultVersionFile()
        // {
        //     if (File.Exists(VERSION_FILE_PATH))
        //     {
        //         bool overwrite = EditorUtility.DisplayDialog(
        //             "版本文件已存在",
        //             $"版本文件已存在: {VERSION_FILE_PATH}\n是否要覆盖？",
        //             "覆盖", "取消"
        //         );

        //         if (!overwrite)
        //         {
        //             return;
        //         }
        //     }

        //     // 创建默认版本数据
        //     VersionData defaultVersion = new VersionData
        //     {
        //         version = "1.0.0",
        //         versionCode = 1,
        //         buildNumber = DateTime.Now.ToString("yyyyMMddHHmmss"),
        //         lastUpdateTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
        //     };

        //     SaveVersionToFile(defaultVersion);

        //     // 在Project窗口中选中文件
        //     var asset = AssetDatabase.LoadAssetAtPath<TextAsset>(VERSION_FILE_PATH);
        //     if (asset != null)
        //     {
        //         EditorGUIUtility.PingObject(asset);
        //         Selection.activeObject = asset;
        //     }

        //     EditorUtility.DisplayDialog("创建成功", $"默认版本文件已创建:\n{VERSION_FILE_PATH}", "确定");
        // }

        // /// <summary>
        // /// 在Inspector中显示版本信息
        // /// </summary>
        // [MenuItem("Build/Show Version Info", priority = 6)]
        // public static void ShowVersionInfo()
        // {
        //     var versionData = VersionManager.GetVersionData();
        //     string info = $"当前版本信息:\n\n" +
        //                  $"版本号: {versionData.version}\n" +
        //                  $"版本代码: {versionData.versionCode}\n" +
        //                  $"构建号: {versionData.buildNumber}\n" +
        //                  $"更新时间: {versionData.lastUpdateTime}";

        //     EditorUtility.DisplayDialog("版本信息", info, "确定");
        // }
    }
}
