using System.Collections.Generic;
public class AOTGenericReferences : UnityEngine.MonoBehaviour
{

	// {{ AOT assemblies
	public static readonly IReadOnlyList<string> PatchedAOTAssemblyList = new List<string>
	{
		"DOTween.dll",
		"Google.Protobuf.dll",
		"Obfuz.Runtime.dll",
		"SimpleInput.Runtime.dll",
		"System.Core.dll",
		"System.dll",
		"UniTask.dll",
		"UnityEngine.AndroidJNIModule.dll",
		"UnityEngine.AssetBundleModule.dll",
		"UnityEngine.CoreModule.dll",
		"YooAsset.dll",
		"mscorlib.dll",
		"spine-unity.dll",
	};
	// }}

	// {{ constraint implement type
	// }} 

	// {{ AOT generic types
	// $D.$ly<$D.$Ky>
	// Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask.<>c<$Dm.$Em>
	// Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask.<>c<$GI.$HI>
	// Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask.<>c<$HF.$IF>
	// Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask.<>c<$Qm.$Um>
	// Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask.<>c<$Qm.$Vm>
	// Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask.<>c<$Qm.$sm>
	// Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask.<>c<$Qm.$um>
	// Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask.<>c<$af.$bf>
	// Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask.<>c<$cI.$fI>
	// Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask.<>c<$gL.$Rm>
	// Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask.<>c<$gk.$VK>
	// Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask.<>c<$jI.$kI>
	// Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask.<>c<$lE.$oE>
	// Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask.<>c<$wo.$Wo>
	// Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask.<>c<PVPBattleRoot.$WD>
	// Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask<$Dm.$Em>
	// Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask<$GI.$HI>
	// Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask<$HF.$IF>
	// Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask<$Qm.$Um>
	// Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask<$Qm.$Vm>
	// Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask<$Qm.$sm>
	// Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask<$Qm.$um>
	// Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask<$af.$bf>
	// Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask<$cI.$fI>
	// Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask<$gL.$Rm>
	// Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask<$gk.$VK>
	// Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask<$jI.$kI>
	// Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask<$lE.$oE>
	// Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask<$wo.$Wo>
	// Cysharp.Threading.Tasks.CompilerServices.AsyncUniTask<PVPBattleRoot.$WD>
	// Cysharp.Threading.Tasks.ITaskPoolNode<object>
	// Cysharp.Threading.Tasks.UniTaskCompletionSourceCore<Cysharp.Threading.Tasks.AsyncUnit>
	// DG.Tweening.Core.DOGetter<float>
	// DG.Tweening.Core.DOSetter<float>
	// Google.Protobuf.Collections.MapField.<>c<object,object>
	// Google.Protobuf.Collections.MapField.<>c__DisplayClass7_0<object,object>
	// Google.Protobuf.Collections.MapField.Codec<object,object>
	// Google.Protobuf.Collections.MapField.DictionaryEnumerator<object,object>
	// Google.Protobuf.Collections.MapField.MapView<object,object,object>
	// Google.Protobuf.Collections.MapField<object,object>
	// Google.Protobuf.Collections.RepeatedField.<GetEnumerator>d__28<int>
	// Google.Protobuf.Collections.RepeatedField.<GetEnumerator>d__28<long>
	// Google.Protobuf.Collections.RepeatedField.<GetEnumerator>d__28<object>
	// Google.Protobuf.Collections.RepeatedField<int>
	// Google.Protobuf.Collections.RepeatedField<long>
	// Google.Protobuf.Collections.RepeatedField<object>
	// Google.Protobuf.FieldCodec.<>c<int>
	// Google.Protobuf.FieldCodec.<>c<long>
	// Google.Protobuf.FieldCodec.<>c<object>
	// Google.Protobuf.FieldCodec.<>c__DisplayClass38_0<int>
	// Google.Protobuf.FieldCodec.<>c__DisplayClass38_0<long>
	// Google.Protobuf.FieldCodec.<>c__DisplayClass38_0<object>
	// Google.Protobuf.FieldCodec.<>c__DisplayClass39_0<int>
	// Google.Protobuf.FieldCodec.<>c__DisplayClass39_0<long>
	// Google.Protobuf.FieldCodec.<>c__DisplayClass39_0<object>
	// Google.Protobuf.FieldCodec.InputMerger<int>
	// Google.Protobuf.FieldCodec.InputMerger<long>
	// Google.Protobuf.FieldCodec.InputMerger<object>
	// Google.Protobuf.FieldCodec.ValuesMerger<int>
	// Google.Protobuf.FieldCodec.ValuesMerger<long>
	// Google.Protobuf.FieldCodec.ValuesMerger<object>
	// Google.Protobuf.FieldCodec<int>
	// Google.Protobuf.FieldCodec<long>
	// Google.Protobuf.FieldCodec<object>
	// Google.Protobuf.IDeepCloneable<int>
	// Google.Protobuf.IDeepCloneable<long>
	// Google.Protobuf.IDeepCloneable<object>
	// Google.Protobuf.IMessage<object>
	// Google.Protobuf.MessageParser.<>c__DisplayClass2_0<object>
	// Google.Protobuf.MessageParser<object>
	// Google.Protobuf.ValueReader<int>
	// Google.Protobuf.ValueReader<long>
	// Google.Protobuf.ValueReader<object>
	// Google.Protobuf.ValueWriter<int>
	// Google.Protobuf.ValueWriter<long>
	// Google.Protobuf.ValueWriter<object>
	// SimpleInputNamespace.BaseInput<object,byte>
	// SimpleInputNamespace.BaseInput<object,float>
	// Spine.ExposedList.Enumerator<object>
	// Spine.ExposedList<object>
	// System.Action<$C.$AV.$bV>
	// System.Action<$C.$Cw.$Dw>
	// System.Action<$C.$Cw.$ew>
	// System.Action<$C.$sw.$Sw>
	// System.Action<$fK.$FK>
	// System.Action<FairyGUI.GPathPoint>
	// System.Action<System.Collections.Generic.KeyValuePair<object,object>>
	// System.Action<UnityEngine.Color32>
	// System.Action<UnityEngine.Rect>
	// System.Action<UnityEngine.SceneManagement.Scene>
	// System.Action<UnityEngine.Vector2,byte>
	// System.Action<UnityEngine.Vector2>
	// System.Action<UnityEngine.Vector3>
	// System.Action<UnityEngine.Vector4>
	// System.Action<byte,int>
	// System.Action<byte,object,object>
	// System.Action<byte,object>
	// System.Action<byte>
	// System.Action<float>
	// System.Action<int,int>
	// System.Action<int,object>
	// System.Action<int>
	// System.Action<long>
	// System.Action<object,object>
	// System.Action<object>
	// System.Action<ushort>
	// System.Collections.Generic.ArraySortHelper<$C.$AV.$bV>
	// System.Collections.Generic.ArraySortHelper<$C.$Cw.$Dw>
	// System.Collections.Generic.ArraySortHelper<$C.$Cw.$ew>
	// System.Collections.Generic.ArraySortHelper<$C.$sw.$Sw>
	// System.Collections.Generic.ArraySortHelper<$fK.$FK>
	// System.Collections.Generic.ArraySortHelper<FairyGUI.GPathPoint>
	// System.Collections.Generic.ArraySortHelper<System.Collections.Generic.KeyValuePair<object,object>>
	// System.Collections.Generic.ArraySortHelper<UnityEngine.Color32>
	// System.Collections.Generic.ArraySortHelper<UnityEngine.Rect>
	// System.Collections.Generic.ArraySortHelper<UnityEngine.Vector2>
	// System.Collections.Generic.ArraySortHelper<UnityEngine.Vector3>
	// System.Collections.Generic.ArraySortHelper<UnityEngine.Vector4>
	// System.Collections.Generic.ArraySortHelper<byte>
	// System.Collections.Generic.ArraySortHelper<float>
	// System.Collections.Generic.ArraySortHelper<int>
	// System.Collections.Generic.ArraySortHelper<long>
	// System.Collections.Generic.ArraySortHelper<object>
	// System.Collections.Generic.ArraySortHelper<ushort>
	// System.Collections.Generic.Comparer<$C.$AV.$bV>
	// System.Collections.Generic.Comparer<$C.$Cw.$Dw>
	// System.Collections.Generic.Comparer<$C.$Cw.$ew>
	// System.Collections.Generic.Comparer<$C.$sw.$Sw>
	// System.Collections.Generic.Comparer<$fK.$FK>
	// System.Collections.Generic.Comparer<FairyGUI.GPathPoint>
	// System.Collections.Generic.Comparer<System.Collections.Generic.KeyValuePair<object,object>>
	// System.Collections.Generic.Comparer<UnityEngine.Color32>
	// System.Collections.Generic.Comparer<UnityEngine.Rect>
	// System.Collections.Generic.Comparer<UnityEngine.Vector2>
	// System.Collections.Generic.Comparer<UnityEngine.Vector3>
	// System.Collections.Generic.Comparer<UnityEngine.Vector4>
	// System.Collections.Generic.Comparer<byte>
	// System.Collections.Generic.Comparer<float>
	// System.Collections.Generic.Comparer<int>
	// System.Collections.Generic.Comparer<long>
	// System.Collections.Generic.Comparer<object>
	// System.Collections.Generic.Comparer<ushort>
	// System.Collections.Generic.Dictionary.Enumerator<int,UnityEngine.Vector2>
	// System.Collections.Generic.Dictionary.Enumerator<int,byte>
	// System.Collections.Generic.Dictionary.Enumerator<int,float>
	// System.Collections.Generic.Dictionary.Enumerator<int,int>
	// System.Collections.Generic.Dictionary.Enumerator<int,long>
	// System.Collections.Generic.Dictionary.Enumerator<int,object>
	// System.Collections.Generic.Dictionary.Enumerator<long,object>
	// System.Collections.Generic.Dictionary.Enumerator<object,System.Nullable<UnityEngine.RaycastHit>>
	// System.Collections.Generic.Dictionary.Enumerator<object,UnityEngine.Vector2>
	// System.Collections.Generic.Dictionary.Enumerator<object,UnityEngine.Vector4>
	// System.Collections.Generic.Dictionary.Enumerator<object,float>
	// System.Collections.Generic.Dictionary.Enumerator<object,int>
	// System.Collections.Generic.Dictionary.Enumerator<object,object>
	// System.Collections.Generic.Dictionary.Enumerator<uint,int>
	// System.Collections.Generic.Dictionary.Enumerator<uint,object>
	// System.Collections.Generic.Dictionary.Enumerator<uint,uint>
	// System.Collections.Generic.Dictionary.KeyCollection.Enumerator<int,UnityEngine.Vector2>
	// System.Collections.Generic.Dictionary.KeyCollection.Enumerator<int,byte>
	// System.Collections.Generic.Dictionary.KeyCollection.Enumerator<int,float>
	// System.Collections.Generic.Dictionary.KeyCollection.Enumerator<int,int>
	// System.Collections.Generic.Dictionary.KeyCollection.Enumerator<int,long>
	// System.Collections.Generic.Dictionary.KeyCollection.Enumerator<int,object>
	// System.Collections.Generic.Dictionary.KeyCollection.Enumerator<long,object>
	// System.Collections.Generic.Dictionary.KeyCollection.Enumerator<object,System.Nullable<UnityEngine.RaycastHit>>
	// System.Collections.Generic.Dictionary.KeyCollection.Enumerator<object,UnityEngine.Vector2>
	// System.Collections.Generic.Dictionary.KeyCollection.Enumerator<object,UnityEngine.Vector4>
	// System.Collections.Generic.Dictionary.KeyCollection.Enumerator<object,float>
	// System.Collections.Generic.Dictionary.KeyCollection.Enumerator<object,int>
	// System.Collections.Generic.Dictionary.KeyCollection.Enumerator<object,object>
	// System.Collections.Generic.Dictionary.KeyCollection.Enumerator<uint,int>
	// System.Collections.Generic.Dictionary.KeyCollection.Enumerator<uint,object>
	// System.Collections.Generic.Dictionary.KeyCollection.Enumerator<uint,uint>
	// System.Collections.Generic.Dictionary.KeyCollection<int,UnityEngine.Vector2>
	// System.Collections.Generic.Dictionary.KeyCollection<int,byte>
	// System.Collections.Generic.Dictionary.KeyCollection<int,float>
	// System.Collections.Generic.Dictionary.KeyCollection<int,int>
	// System.Collections.Generic.Dictionary.KeyCollection<int,long>
	// System.Collections.Generic.Dictionary.KeyCollection<int,object>
	// System.Collections.Generic.Dictionary.KeyCollection<long,object>
	// System.Collections.Generic.Dictionary.KeyCollection<object,System.Nullable<UnityEngine.RaycastHit>>
	// System.Collections.Generic.Dictionary.KeyCollection<object,UnityEngine.Vector2>
	// System.Collections.Generic.Dictionary.KeyCollection<object,UnityEngine.Vector4>
	// System.Collections.Generic.Dictionary.KeyCollection<object,float>
	// System.Collections.Generic.Dictionary.KeyCollection<object,int>
	// System.Collections.Generic.Dictionary.KeyCollection<object,object>
	// System.Collections.Generic.Dictionary.KeyCollection<uint,int>
	// System.Collections.Generic.Dictionary.KeyCollection<uint,object>
	// System.Collections.Generic.Dictionary.KeyCollection<uint,uint>
	// System.Collections.Generic.Dictionary.ValueCollection.Enumerator<int,UnityEngine.Vector2>
	// System.Collections.Generic.Dictionary.ValueCollection.Enumerator<int,byte>
	// System.Collections.Generic.Dictionary.ValueCollection.Enumerator<int,float>
	// System.Collections.Generic.Dictionary.ValueCollection.Enumerator<int,int>
	// System.Collections.Generic.Dictionary.ValueCollection.Enumerator<int,long>
	// System.Collections.Generic.Dictionary.ValueCollection.Enumerator<int,object>
	// System.Collections.Generic.Dictionary.ValueCollection.Enumerator<long,object>
	// System.Collections.Generic.Dictionary.ValueCollection.Enumerator<object,System.Nullable<UnityEngine.RaycastHit>>
	// System.Collections.Generic.Dictionary.ValueCollection.Enumerator<object,UnityEngine.Vector2>
	// System.Collections.Generic.Dictionary.ValueCollection.Enumerator<object,UnityEngine.Vector4>
	// System.Collections.Generic.Dictionary.ValueCollection.Enumerator<object,float>
	// System.Collections.Generic.Dictionary.ValueCollection.Enumerator<object,int>
	// System.Collections.Generic.Dictionary.ValueCollection.Enumerator<object,object>
	// System.Collections.Generic.Dictionary.ValueCollection.Enumerator<uint,int>
	// System.Collections.Generic.Dictionary.ValueCollection.Enumerator<uint,object>
	// System.Collections.Generic.Dictionary.ValueCollection.Enumerator<uint,uint>
	// System.Collections.Generic.Dictionary.ValueCollection<int,UnityEngine.Vector2>
	// System.Collections.Generic.Dictionary.ValueCollection<int,byte>
	// System.Collections.Generic.Dictionary.ValueCollection<int,float>
	// System.Collections.Generic.Dictionary.ValueCollection<int,int>
	// System.Collections.Generic.Dictionary.ValueCollection<int,long>
	// System.Collections.Generic.Dictionary.ValueCollection<int,object>
	// System.Collections.Generic.Dictionary.ValueCollection<long,object>
	// System.Collections.Generic.Dictionary.ValueCollection<object,System.Nullable<UnityEngine.RaycastHit>>
	// System.Collections.Generic.Dictionary.ValueCollection<object,UnityEngine.Vector2>
	// System.Collections.Generic.Dictionary.ValueCollection<object,UnityEngine.Vector4>
	// System.Collections.Generic.Dictionary.ValueCollection<object,float>
	// System.Collections.Generic.Dictionary.ValueCollection<object,int>
	// System.Collections.Generic.Dictionary.ValueCollection<object,object>
	// System.Collections.Generic.Dictionary.ValueCollection<uint,int>
	// System.Collections.Generic.Dictionary.ValueCollection<uint,object>
	// System.Collections.Generic.Dictionary.ValueCollection<uint,uint>
	// System.Collections.Generic.Dictionary<int,UnityEngine.Vector2>
	// System.Collections.Generic.Dictionary<int,byte>
	// System.Collections.Generic.Dictionary<int,float>
	// System.Collections.Generic.Dictionary<int,int>
	// System.Collections.Generic.Dictionary<int,long>
	// System.Collections.Generic.Dictionary<int,object>
	// System.Collections.Generic.Dictionary<long,object>
	// System.Collections.Generic.Dictionary<object,System.Nullable<UnityEngine.RaycastHit>>
	// System.Collections.Generic.Dictionary<object,UnityEngine.Vector2>
	// System.Collections.Generic.Dictionary<object,UnityEngine.Vector4>
	// System.Collections.Generic.Dictionary<object,float>
	// System.Collections.Generic.Dictionary<object,int>
	// System.Collections.Generic.Dictionary<object,object>
	// System.Collections.Generic.Dictionary<uint,int>
	// System.Collections.Generic.Dictionary<uint,object>
	// System.Collections.Generic.Dictionary<uint,uint>
	// System.Collections.Generic.EqualityComparer<System.Collections.Generic.KeyValuePair<object,object>>
	// System.Collections.Generic.EqualityComparer<System.Nullable<UnityEngine.RaycastHit>>
	// System.Collections.Generic.EqualityComparer<UnityEngine.Vector2>
	// System.Collections.Generic.EqualityComparer<UnityEngine.Vector4>
	// System.Collections.Generic.EqualityComparer<byte>
	// System.Collections.Generic.EqualityComparer<double>
	// System.Collections.Generic.EqualityComparer<float>
	// System.Collections.Generic.EqualityComparer<int>
	// System.Collections.Generic.EqualityComparer<long>
	// System.Collections.Generic.EqualityComparer<object>
	// System.Collections.Generic.EqualityComparer<uint>
	// System.Collections.Generic.HashSet.Enumerator<object>
	// System.Collections.Generic.HashSet<object>
	// System.Collections.Generic.HashSetEqualityComparer<object>
	// System.Collections.Generic.ICollection<$C.$AV.$bV>
	// System.Collections.Generic.ICollection<$C.$Cw.$Dw>
	// System.Collections.Generic.ICollection<$C.$Cw.$ew>
	// System.Collections.Generic.ICollection<$C.$sw.$Sw>
	// System.Collections.Generic.ICollection<$fK.$FK>
	// System.Collections.Generic.ICollection<FairyGUI.GPathPoint>
	// System.Collections.Generic.ICollection<System.Collections.Generic.KeyValuePair<int,UnityEngine.Vector2>>
	// System.Collections.Generic.ICollection<System.Collections.Generic.KeyValuePair<int,byte>>
	// System.Collections.Generic.ICollection<System.Collections.Generic.KeyValuePair<int,float>>
	// System.Collections.Generic.ICollection<System.Collections.Generic.KeyValuePair<int,int>>
	// System.Collections.Generic.ICollection<System.Collections.Generic.KeyValuePair<int,long>>
	// System.Collections.Generic.ICollection<System.Collections.Generic.KeyValuePair<int,object>>
	// System.Collections.Generic.ICollection<System.Collections.Generic.KeyValuePair<long,object>>
	// System.Collections.Generic.ICollection<System.Collections.Generic.KeyValuePair<object,System.Nullable<UnityEngine.RaycastHit>>>
	// System.Collections.Generic.ICollection<System.Collections.Generic.KeyValuePair<object,UnityEngine.Vector2>>
	// System.Collections.Generic.ICollection<System.Collections.Generic.KeyValuePair<object,UnityEngine.Vector4>>
	// System.Collections.Generic.ICollection<System.Collections.Generic.KeyValuePair<object,float>>
	// System.Collections.Generic.ICollection<System.Collections.Generic.KeyValuePair<object,int>>
	// System.Collections.Generic.ICollection<System.Collections.Generic.KeyValuePair<object,object>>
	// System.Collections.Generic.ICollection<System.Collections.Generic.KeyValuePair<uint,int>>
	// System.Collections.Generic.ICollection<System.Collections.Generic.KeyValuePair<uint,object>>
	// System.Collections.Generic.ICollection<System.Collections.Generic.KeyValuePair<uint,uint>>
	// System.Collections.Generic.ICollection<UnityEngine.Color32>
	// System.Collections.Generic.ICollection<UnityEngine.Rect>
	// System.Collections.Generic.ICollection<UnityEngine.Vector2>
	// System.Collections.Generic.ICollection<UnityEngine.Vector3>
	// System.Collections.Generic.ICollection<UnityEngine.Vector4>
	// System.Collections.Generic.ICollection<byte>
	// System.Collections.Generic.ICollection<float>
	// System.Collections.Generic.ICollection<int>
	// System.Collections.Generic.ICollection<long>
	// System.Collections.Generic.ICollection<object>
	// System.Collections.Generic.ICollection<ushort>
	// System.Collections.Generic.IComparer<$C.$AV.$bV>
	// System.Collections.Generic.IComparer<$C.$Cw.$Dw>
	// System.Collections.Generic.IComparer<$C.$Cw.$ew>
	// System.Collections.Generic.IComparer<$C.$sw.$Sw>
	// System.Collections.Generic.IComparer<$fK.$FK>
	// System.Collections.Generic.IComparer<FairyGUI.GPathPoint>
	// System.Collections.Generic.IComparer<System.Collections.Generic.KeyValuePair<object,object>>
	// System.Collections.Generic.IComparer<UnityEngine.Color32>
	// System.Collections.Generic.IComparer<UnityEngine.Rect>
	// System.Collections.Generic.IComparer<UnityEngine.Vector2>
	// System.Collections.Generic.IComparer<UnityEngine.Vector3>
	// System.Collections.Generic.IComparer<UnityEngine.Vector4>
	// System.Collections.Generic.IComparer<byte>
	// System.Collections.Generic.IComparer<float>
	// System.Collections.Generic.IComparer<int>
	// System.Collections.Generic.IComparer<long>
	// System.Collections.Generic.IComparer<object>
	// System.Collections.Generic.IComparer<ushort>
	// System.Collections.Generic.IEnumerable<$C.$AV.$bV>
	// System.Collections.Generic.IEnumerable<$C.$Cw.$Dw>
	// System.Collections.Generic.IEnumerable<$C.$Cw.$ew>
	// System.Collections.Generic.IEnumerable<$C.$sw.$Sw>
	// System.Collections.Generic.IEnumerable<$fK.$FK>
	// System.Collections.Generic.IEnumerable<FairyGUI.GPathPoint>
	// System.Collections.Generic.IEnumerable<System.Collections.Generic.KeyValuePair<int,UnityEngine.Vector2>>
	// System.Collections.Generic.IEnumerable<System.Collections.Generic.KeyValuePair<int,byte>>
	// System.Collections.Generic.IEnumerable<System.Collections.Generic.KeyValuePair<int,float>>
	// System.Collections.Generic.IEnumerable<System.Collections.Generic.KeyValuePair<int,int>>
	// System.Collections.Generic.IEnumerable<System.Collections.Generic.KeyValuePair<int,long>>
	// System.Collections.Generic.IEnumerable<System.Collections.Generic.KeyValuePair<int,object>>
	// System.Collections.Generic.IEnumerable<System.Collections.Generic.KeyValuePair<long,object>>
	// System.Collections.Generic.IEnumerable<System.Collections.Generic.KeyValuePair<object,System.Nullable<UnityEngine.RaycastHit>>>
	// System.Collections.Generic.IEnumerable<System.Collections.Generic.KeyValuePair<object,UnityEngine.Vector2>>
	// System.Collections.Generic.IEnumerable<System.Collections.Generic.KeyValuePair<object,UnityEngine.Vector4>>
	// System.Collections.Generic.IEnumerable<System.Collections.Generic.KeyValuePair<object,float>>
	// System.Collections.Generic.IEnumerable<System.Collections.Generic.KeyValuePair<object,int>>
	// System.Collections.Generic.IEnumerable<System.Collections.Generic.KeyValuePair<object,object>>
	// System.Collections.Generic.IEnumerable<System.Collections.Generic.KeyValuePair<uint,int>>
	// System.Collections.Generic.IEnumerable<System.Collections.Generic.KeyValuePair<uint,object>>
	// System.Collections.Generic.IEnumerable<System.Collections.Generic.KeyValuePair<uint,uint>>
	// System.Collections.Generic.IEnumerable<UnityEngine.Color32>
	// System.Collections.Generic.IEnumerable<UnityEngine.Rect>
	// System.Collections.Generic.IEnumerable<UnityEngine.Vector2>
	// System.Collections.Generic.IEnumerable<UnityEngine.Vector3>
	// System.Collections.Generic.IEnumerable<UnityEngine.Vector4>
	// System.Collections.Generic.IEnumerable<byte>
	// System.Collections.Generic.IEnumerable<float>
	// System.Collections.Generic.IEnumerable<int>
	// System.Collections.Generic.IEnumerable<long>
	// System.Collections.Generic.IEnumerable<object>
	// System.Collections.Generic.IEnumerable<ushort>
	// System.Collections.Generic.IEnumerator<$C.$AV.$bV>
	// System.Collections.Generic.IEnumerator<$C.$Cw.$Dw>
	// System.Collections.Generic.IEnumerator<$C.$Cw.$ew>
	// System.Collections.Generic.IEnumerator<$C.$sw.$Sw>
	// System.Collections.Generic.IEnumerator<$fK.$FK>
	// System.Collections.Generic.IEnumerator<FairyGUI.GPathPoint>
	// System.Collections.Generic.IEnumerator<System.Collections.Generic.KeyValuePair<int,UnityEngine.Vector2>>
	// System.Collections.Generic.IEnumerator<System.Collections.Generic.KeyValuePair<int,byte>>
	// System.Collections.Generic.IEnumerator<System.Collections.Generic.KeyValuePair<int,float>>
	// System.Collections.Generic.IEnumerator<System.Collections.Generic.KeyValuePair<int,int>>
	// System.Collections.Generic.IEnumerator<System.Collections.Generic.KeyValuePair<int,long>>
	// System.Collections.Generic.IEnumerator<System.Collections.Generic.KeyValuePair<int,object>>
	// System.Collections.Generic.IEnumerator<System.Collections.Generic.KeyValuePair<long,object>>
	// System.Collections.Generic.IEnumerator<System.Collections.Generic.KeyValuePair<object,System.Nullable<UnityEngine.RaycastHit>>>
	// System.Collections.Generic.IEnumerator<System.Collections.Generic.KeyValuePair<object,UnityEngine.Vector2>>
	// System.Collections.Generic.IEnumerator<System.Collections.Generic.KeyValuePair<object,UnityEngine.Vector4>>
	// System.Collections.Generic.IEnumerator<System.Collections.Generic.KeyValuePair<object,float>>
	// System.Collections.Generic.IEnumerator<System.Collections.Generic.KeyValuePair<object,int>>
	// System.Collections.Generic.IEnumerator<System.Collections.Generic.KeyValuePair<object,object>>
	// System.Collections.Generic.IEnumerator<System.Collections.Generic.KeyValuePair<uint,int>>
	// System.Collections.Generic.IEnumerator<System.Collections.Generic.KeyValuePair<uint,object>>
	// System.Collections.Generic.IEnumerator<System.Collections.Generic.KeyValuePair<uint,uint>>
	// System.Collections.Generic.IEnumerator<UnityEngine.Color32>
	// System.Collections.Generic.IEnumerator<UnityEngine.Rect>
	// System.Collections.Generic.IEnumerator<UnityEngine.Vector2>
	// System.Collections.Generic.IEnumerator<UnityEngine.Vector3>
	// System.Collections.Generic.IEnumerator<UnityEngine.Vector4>
	// System.Collections.Generic.IEnumerator<byte>
	// System.Collections.Generic.IEnumerator<float>
	// System.Collections.Generic.IEnumerator<int>
	// System.Collections.Generic.IEnumerator<long>
	// System.Collections.Generic.IEnumerator<object>
	// System.Collections.Generic.IEnumerator<ushort>
	// System.Collections.Generic.IEqualityComparer<int>
	// System.Collections.Generic.IEqualityComparer<long>
	// System.Collections.Generic.IEqualityComparer<object>
	// System.Collections.Generic.IEqualityComparer<uint>
	// System.Collections.Generic.IList<$C.$AV.$bV>
	// System.Collections.Generic.IList<$C.$Cw.$Dw>
	// System.Collections.Generic.IList<$C.$Cw.$ew>
	// System.Collections.Generic.IList<$C.$sw.$Sw>
	// System.Collections.Generic.IList<$fK.$FK>
	// System.Collections.Generic.IList<FairyGUI.GPathPoint>
	// System.Collections.Generic.IList<System.Collections.Generic.KeyValuePair<object,object>>
	// System.Collections.Generic.IList<UnityEngine.Color32>
	// System.Collections.Generic.IList<UnityEngine.Rect>
	// System.Collections.Generic.IList<UnityEngine.Vector2>
	// System.Collections.Generic.IList<UnityEngine.Vector3>
	// System.Collections.Generic.IList<UnityEngine.Vector4>
	// System.Collections.Generic.IList<byte>
	// System.Collections.Generic.IList<float>
	// System.Collections.Generic.IList<int>
	// System.Collections.Generic.IList<long>
	// System.Collections.Generic.IList<object>
	// System.Collections.Generic.IList<ushort>
	// System.Collections.Generic.KeyValuePair<int,UnityEngine.Vector2>
	// System.Collections.Generic.KeyValuePair<int,byte>
	// System.Collections.Generic.KeyValuePair<int,float>
	// System.Collections.Generic.KeyValuePair<int,int>
	// System.Collections.Generic.KeyValuePair<int,long>
	// System.Collections.Generic.KeyValuePair<int,object>
	// System.Collections.Generic.KeyValuePair<long,object>
	// System.Collections.Generic.KeyValuePair<object,System.Nullable<UnityEngine.RaycastHit>>
	// System.Collections.Generic.KeyValuePair<object,UnityEngine.Vector2>
	// System.Collections.Generic.KeyValuePair<object,UnityEngine.Vector4>
	// System.Collections.Generic.KeyValuePair<object,float>
	// System.Collections.Generic.KeyValuePair<object,int>
	// System.Collections.Generic.KeyValuePair<object,object>
	// System.Collections.Generic.KeyValuePair<uint,int>
	// System.Collections.Generic.KeyValuePair<uint,object>
	// System.Collections.Generic.KeyValuePair<uint,uint>
	// System.Collections.Generic.LinkedList.Enumerator<System.Collections.Generic.KeyValuePair<object,object>>
	// System.Collections.Generic.LinkedList<System.Collections.Generic.KeyValuePair<object,object>>
	// System.Collections.Generic.LinkedListNode<System.Collections.Generic.KeyValuePair<object,object>>
	// System.Collections.Generic.List.Enumerator<$C.$AV.$bV>
	// System.Collections.Generic.List.Enumerator<$C.$Cw.$Dw>
	// System.Collections.Generic.List.Enumerator<$C.$Cw.$ew>
	// System.Collections.Generic.List.Enumerator<$C.$sw.$Sw>
	// System.Collections.Generic.List.Enumerator<$fK.$FK>
	// System.Collections.Generic.List.Enumerator<FairyGUI.GPathPoint>
	// System.Collections.Generic.List.Enumerator<System.Collections.Generic.KeyValuePair<object,object>>
	// System.Collections.Generic.List.Enumerator<UnityEngine.Color32>
	// System.Collections.Generic.List.Enumerator<UnityEngine.Rect>
	// System.Collections.Generic.List.Enumerator<UnityEngine.Vector2>
	// System.Collections.Generic.List.Enumerator<UnityEngine.Vector3>
	// System.Collections.Generic.List.Enumerator<UnityEngine.Vector4>
	// System.Collections.Generic.List.Enumerator<byte>
	// System.Collections.Generic.List.Enumerator<float>
	// System.Collections.Generic.List.Enumerator<int>
	// System.Collections.Generic.List.Enumerator<long>
	// System.Collections.Generic.List.Enumerator<object>
	// System.Collections.Generic.List.Enumerator<ushort>
	// System.Collections.Generic.List<$C.$AV.$bV>
	// System.Collections.Generic.List<$C.$Cw.$Dw>
	// System.Collections.Generic.List<$C.$Cw.$ew>
	// System.Collections.Generic.List<$C.$sw.$Sw>
	// System.Collections.Generic.List<$fK.$FK>
	// System.Collections.Generic.List<FairyGUI.GPathPoint>
	// System.Collections.Generic.List<System.Collections.Generic.KeyValuePair<object,object>>
	// System.Collections.Generic.List<UnityEngine.Color32>
	// System.Collections.Generic.List<UnityEngine.Rect>
	// System.Collections.Generic.List<UnityEngine.Vector2>
	// System.Collections.Generic.List<UnityEngine.Vector3>
	// System.Collections.Generic.List<UnityEngine.Vector4>
	// System.Collections.Generic.List<byte>
	// System.Collections.Generic.List<float>
	// System.Collections.Generic.List<int>
	// System.Collections.Generic.List<long>
	// System.Collections.Generic.List<object>
	// System.Collections.Generic.List<ushort>
	// System.Collections.Generic.ObjectComparer<$C.$AV.$bV>
	// System.Collections.Generic.ObjectComparer<$C.$Cw.$Dw>
	// System.Collections.Generic.ObjectComparer<$C.$Cw.$ew>
	// System.Collections.Generic.ObjectComparer<$C.$sw.$Sw>
	// System.Collections.Generic.ObjectComparer<$fK.$FK>
	// System.Collections.Generic.ObjectComparer<FairyGUI.GPathPoint>
	// System.Collections.Generic.ObjectComparer<System.Collections.Generic.KeyValuePair<object,object>>
	// System.Collections.Generic.ObjectComparer<UnityEngine.Color32>
	// System.Collections.Generic.ObjectComparer<UnityEngine.Rect>
	// System.Collections.Generic.ObjectComparer<UnityEngine.Vector2>
	// System.Collections.Generic.ObjectComparer<UnityEngine.Vector3>
	// System.Collections.Generic.ObjectComparer<UnityEngine.Vector4>
	// System.Collections.Generic.ObjectComparer<byte>
	// System.Collections.Generic.ObjectComparer<float>
	// System.Collections.Generic.ObjectComparer<int>
	// System.Collections.Generic.ObjectComparer<long>
	// System.Collections.Generic.ObjectComparer<object>
	// System.Collections.Generic.ObjectComparer<ushort>
	// System.Collections.Generic.ObjectEqualityComparer<System.Collections.Generic.KeyValuePair<object,object>>
	// System.Collections.Generic.ObjectEqualityComparer<System.Nullable<UnityEngine.RaycastHit>>
	// System.Collections.Generic.ObjectEqualityComparer<UnityEngine.Vector2>
	// System.Collections.Generic.ObjectEqualityComparer<UnityEngine.Vector4>
	// System.Collections.Generic.ObjectEqualityComparer<byte>
	// System.Collections.Generic.ObjectEqualityComparer<double>
	// System.Collections.Generic.ObjectEqualityComparer<float>
	// System.Collections.Generic.ObjectEqualityComparer<int>
	// System.Collections.Generic.ObjectEqualityComparer<long>
	// System.Collections.Generic.ObjectEqualityComparer<object>
	// System.Collections.Generic.ObjectEqualityComparer<uint>
	// System.Collections.Generic.Queue.Enumerator<int>
	// System.Collections.Generic.Queue.Enumerator<object>
	// System.Collections.Generic.Queue<int>
	// System.Collections.Generic.Queue<object>
	// System.Collections.Generic.Stack.Enumerator<$C.$hw.$Hw>
	// System.Collections.Generic.Stack.Enumerator<object>
	// System.Collections.Generic.Stack<$C.$hw.$Hw>
	// System.Collections.Generic.Stack<object>
	// System.Collections.ObjectModel.ReadOnlyCollection<$C.$AV.$bV>
	// System.Collections.ObjectModel.ReadOnlyCollection<$C.$Cw.$Dw>
	// System.Collections.ObjectModel.ReadOnlyCollection<$C.$Cw.$ew>
	// System.Collections.ObjectModel.ReadOnlyCollection<$C.$sw.$Sw>
	// System.Collections.ObjectModel.ReadOnlyCollection<$fK.$FK>
	// System.Collections.ObjectModel.ReadOnlyCollection<FairyGUI.GPathPoint>
	// System.Collections.ObjectModel.ReadOnlyCollection<System.Collections.Generic.KeyValuePair<object,object>>
	// System.Collections.ObjectModel.ReadOnlyCollection<UnityEngine.Color32>
	// System.Collections.ObjectModel.ReadOnlyCollection<UnityEngine.Rect>
	// System.Collections.ObjectModel.ReadOnlyCollection<UnityEngine.Vector2>
	// System.Collections.ObjectModel.ReadOnlyCollection<UnityEngine.Vector3>
	// System.Collections.ObjectModel.ReadOnlyCollection<UnityEngine.Vector4>
	// System.Collections.ObjectModel.ReadOnlyCollection<byte>
	// System.Collections.ObjectModel.ReadOnlyCollection<float>
	// System.Collections.ObjectModel.ReadOnlyCollection<int>
	// System.Collections.ObjectModel.ReadOnlyCollection<long>
	// System.Collections.ObjectModel.ReadOnlyCollection<object>
	// System.Collections.ObjectModel.ReadOnlyCollection<ushort>
	// System.Comparison<$C.$AV.$bV>
	// System.Comparison<$C.$Cw.$Dw>
	// System.Comparison<$C.$Cw.$ew>
	// System.Comparison<$C.$sw.$Sw>
	// System.Comparison<$fK.$FK>
	// System.Comparison<FairyGUI.GPathPoint>
	// System.Comparison<System.Collections.Generic.KeyValuePair<object,object>>
	// System.Comparison<UnityEngine.Color32>
	// System.Comparison<UnityEngine.Rect>
	// System.Comparison<UnityEngine.Vector2>
	// System.Comparison<UnityEngine.Vector3>
	// System.Comparison<UnityEngine.Vector4>
	// System.Comparison<byte>
	// System.Comparison<float>
	// System.Comparison<int>
	// System.Comparison<long>
	// System.Comparison<object>
	// System.Comparison<ushort>
	// System.EventHandler<object>
	// System.Func<$fK.$FK,byte>
	// System.Func<$fK.$FK,int>
	// System.Func<System.Collections.Generic.KeyValuePair<object,object>,System.Collections.DictionaryEntry>
	// System.Func<System.Collections.Generic.KeyValuePair<object,object>,byte>
	// System.Func<System.Collections.Generic.KeyValuePair<object,object>,object>
	// System.Func<System.Threading.Tasks.VoidTaskResult>
	// System.Func<byte>
	// System.Func<int,byte>
	// System.Func<int,int>
	// System.Func<int>
	// System.Func<long,int>
	// System.Func<object,System.Threading.Tasks.VoidTaskResult>
	// System.Func<object,byte>
	// System.Func<object,int>
	// System.Func<object,object,object>
	// System.Func<object,object>
	// System.Func<object,uint>
	// System.Func<object>
	// System.IComparable<SimpleObscuredDouble>
	// System.IComparable<SimpleObscuredFloat>
	// System.IComparable<SimpleObscuredInt>
	// System.IComparable<SimpleObscuredLong>
	// System.IComparable<double>
	// System.IComparable<float>
	// System.IComparable<int>
	// System.IComparable<long>
	// System.IEquatable<SimpleObscuredDouble>
	// System.IEquatable<SimpleObscuredFloat>
	// System.IEquatable<SimpleObscuredInt>
	// System.IEquatable<SimpleObscuredLong>
	// System.IEquatable<double>
	// System.IEquatable<float>
	// System.IEquatable<int>
	// System.IEquatable<long>
	// System.IEquatable<object>
	// System.Linq.Buffer<System.Collections.Generic.KeyValuePair<int,object>>
	// System.Linq.Buffer<int>
	// System.Linq.Buffer<object>
	// System.Linq.Enumerable.<ReverseIterator>d__79<object>
	// System.Linq.Enumerable.<SkipIterator>d__31<object>
	// System.Linq.Enumerable.<TakeIterator>d__25<object>
	// System.Linq.Enumerable.Iterator<$fK.$FK>
	// System.Linq.Enumerable.Iterator<int>
	// System.Linq.Enumerable.Iterator<object>
	// System.Linq.Enumerable.WhereArrayIterator<object>
	// System.Linq.Enumerable.WhereEnumerableIterator<int>
	// System.Linq.Enumerable.WhereEnumerableIterator<object>
	// System.Linq.Enumerable.WhereListIterator<object>
	// System.Linq.Enumerable.WhereSelectArrayIterator<$fK.$FK,int>
	// System.Linq.Enumerable.WhereSelectArrayIterator<object,int>
	// System.Linq.Enumerable.WhereSelectEnumerableIterator<$fK.$FK,int>
	// System.Linq.Enumerable.WhereSelectEnumerableIterator<object,int>
	// System.Linq.Enumerable.WhereSelectListIterator<$fK.$FK,int>
	// System.Linq.Enumerable.WhereSelectListIterator<object,int>
	// System.Linq.EnumerableSorter<int,int>
	// System.Linq.EnumerableSorter<int>
	// System.Linq.EnumerableSorter<object,byte>
	// System.Linq.EnumerableSorter<object,int>
	// System.Linq.EnumerableSorter<object,object>
	// System.Linq.EnumerableSorter<object>
	// System.Linq.IOrderedEnumerable<object>
	// System.Linq.OrderedEnumerable.<GetEnumerator>d__1<int>
	// System.Linq.OrderedEnumerable.<GetEnumerator>d__1<object>
	// System.Linq.OrderedEnumerable<int,int>
	// System.Linq.OrderedEnumerable<int>
	// System.Linq.OrderedEnumerable<object,byte>
	// System.Linq.OrderedEnumerable<object,int>
	// System.Linq.OrderedEnumerable<object,object>
	// System.Linq.OrderedEnumerable<object>
	// System.Nullable<$C.$Qx>
	// System.Nullable<UnityEngine.Color32>
	// System.Nullable<UnityEngine.RaycastHit>
	// System.Nullable<UnityEngine.Rect>
	// System.Nullable<UnityEngine.Vector2>
	// System.Nullable<UnityEngine.Vector3>
	// System.Nullable<UnityEngine.Vector4>
	// System.Predicate<$C.$AV.$bV>
	// System.Predicate<$C.$Cw.$Dw>
	// System.Predicate<$C.$Cw.$ew>
	// System.Predicate<$C.$sw.$Sw>
	// System.Predicate<$fK.$FK>
	// System.Predicate<FairyGUI.GPathPoint>
	// System.Predicate<System.Collections.Generic.KeyValuePair<object,object>>
	// System.Predicate<UnityEngine.Color32>
	// System.Predicate<UnityEngine.Rect>
	// System.Predicate<UnityEngine.Vector2>
	// System.Predicate<UnityEngine.Vector3>
	// System.Predicate<UnityEngine.Vector4>
	// System.Predicate<byte>
	// System.Predicate<float>
	// System.Predicate<int>
	// System.Predicate<long>
	// System.Predicate<object>
	// System.Predicate<ushort>
	// System.Runtime.CompilerServices.AsyncTaskMethodBuilder<System.Threading.Tasks.VoidTaskResult>
	// System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter<System.Threading.Tasks.VoidTaskResult>
	// System.Runtime.CompilerServices.ConfiguredTaskAwaitable.ConfiguredTaskAwaiter<byte>
	// System.Runtime.CompilerServices.ConfiguredTaskAwaitable<System.Threading.Tasks.VoidTaskResult>
	// System.Runtime.CompilerServices.ConfiguredTaskAwaitable<byte>
	// System.Runtime.CompilerServices.TaskAwaiter<System.Threading.Tasks.VoidTaskResult>
	// System.Runtime.CompilerServices.TaskAwaiter<byte>
	// System.Threading.Tasks.ContinuationTaskFromResultTask<System.Threading.Tasks.VoidTaskResult>
	// System.Threading.Tasks.ContinuationTaskFromResultTask<byte>
	// System.Threading.Tasks.Task<System.Threading.Tasks.VoidTaskResult>
	// System.Threading.Tasks.Task<byte>
	// System.Threading.Tasks.TaskCompletionSource<byte>
	// System.Threading.Tasks.TaskFactory.<>c__DisplayClass35_0<System.Threading.Tasks.VoidTaskResult>
	// System.Threading.Tasks.TaskFactory.<>c__DisplayClass35_0<byte>
	// System.Threading.Tasks.TaskFactory<System.Threading.Tasks.VoidTaskResult>
	// System.Threading.Tasks.TaskFactory<byte>
	// System.ValueTuple<object,object>
	// UnityEngine.Events.InvokableCall<object>
	// UnityEngine.Events.UnityAction<UnityEngine.SceneManagement.Scene,int>
	// UnityEngine.Events.UnityAction<object>
	// UnityEngine.Events.UnityEvent<object>
	// UnityEngine.Pool.CollectionPool.<>c<object,object>
	// UnityEngine.Pool.CollectionPool<object,object>
	// }}

	public void RefMethods()
	{
		// System.Void Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$Dm.$Em>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$Dm.$Em&)
		// System.Void Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$GI.$HI>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$GI.$HI&)
		// System.Void Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$HF.$IF>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$HF.$IF&)
		// System.Void Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$Qm.$Um>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$Qm.$Um&)
		// System.Void Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$Qm.$Vm>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$Qm.$Vm&)
		// System.Void Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$Qm.$sm>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$Qm.$sm&)
		// System.Void Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$Qm.$um>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$Qm.$um&)
		// System.Void Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$af.$bf>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$af.$bf&)
		// System.Void Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$cI.$fI>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$cI.$fI&)
		// System.Void Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$gL.$Rm>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$gL.$Rm&)
		// System.Void Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$gk.$VK>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$gk.$VK&)
		// System.Void Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$jI.$kI>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$jI.$kI&)
		// System.Void Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$lE.$oE>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$lE.$oE&)
		// System.Void Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$wo.$Wo>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$wo.$Wo&)
		// System.Void Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,PVPBattleRoot.$WD>(Cysharp.Threading.Tasks.UniTask.Awaiter&,PVPBattleRoot.$WD&)
		// System.Void Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder.Start<$Dm.$Em>($Dm.$Em&)
		// System.Void Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder.Start<$GI.$HI>($GI.$HI&)
		// System.Void Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder.Start<$HF.$IF>($HF.$IF&)
		// System.Void Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder.Start<$Qm.$Um>($Qm.$Um&)
		// System.Void Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder.Start<$Qm.$Vm>($Qm.$Vm&)
		// System.Void Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder.Start<$Qm.$sm>($Qm.$sm&)
		// System.Void Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder.Start<$Qm.$um>($Qm.$um&)
		// System.Void Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder.Start<$af.$bf>($af.$bf&)
		// System.Void Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder.Start<$cI.$fI>($cI.$fI&)
		// System.Void Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder.Start<$gL.$Rm>($gL.$Rm&)
		// System.Void Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder.Start<$gk.$VK>($gk.$VK&)
		// System.Void Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder.Start<$jI.$kI>($jI.$kI&)
		// System.Void Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder.Start<$lE.$oE>($lE.$oE&)
		// System.Void Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder.Start<$wo.$Wo>($wo.$Wo&)
		// System.Void Cysharp.Threading.Tasks.CompilerServices.AsyncUniTaskMethodBuilder.Start<PVPBattleRoot.$WD>(PVPBattleRoot.$WD&)
		// object DG.Tweening.TweenSettingsExtensions.OnComplete<object>(object,DG.Tweening.TweenCallback)
		// object DG.Tweening.TweenSettingsExtensions.OnRewind<object>(object,DG.Tweening.TweenCallback)
		// object DG.Tweening.TweenSettingsExtensions.OnUpdate<object>(object,DG.Tweening.TweenCallback)
		// object DG.Tweening.TweenSettingsExtensions.SetAutoKill<object>(object,bool)
		// object DG.Tweening.TweenSettingsExtensions.SetEase<object>(object,DG.Tweening.Ease)
		// object DG.Tweening.TweenSettingsExtensions.SetId<object>(object,int)
		// object DG.Tweening.TweenSettingsExtensions.SetLink<object>(object,UnityEngine.GameObject)
		// object DG.Tweening.TweenSettingsExtensions.SetTarget<object>(object,object)
		// object Spine.Unity.SkeletonRenderer.AddSpineComponent<object>(UnityEngine.GameObject,Spine.Unity.SkeletonDataAsset)
		// object Spine.Unity.SkeletonRenderer.NewSpineGameObject<object>(Spine.Unity.SkeletonDataAsset)
		// object System.Activator.CreateInstance<object>()
		// object[] System.Array.Empty<object>()
		// int System.Array.IndexOf<object>(object[],object)
		// int System.Array.IndexOfImpl<object>(object[],object,int,int)
		// System.Void System.Array.Resize<object>(object[]&,int)
		// System.Void System.Array.Sort<UnityEngine.Vector2>(UnityEngine.Vector2[],System.Comparison<UnityEngine.Vector2>)
		// bool System.Linq.Enumerable.Any<object>(System.Collections.Generic.IEnumerable<object>,System.Func<object,bool>)
		// object System.Linq.Enumerable.FirstOrDefault<object>(System.Collections.Generic.IEnumerable<object>,System.Func<object,bool>)
		// object System.Linq.Enumerable.Last<object>(System.Collections.Generic.IEnumerable<object>)
		// System.Linq.IOrderedEnumerable<int> System.Linq.Enumerable.OrderBy<int,int>(System.Collections.Generic.IEnumerable<int>,System.Func<int,int>)
		// System.Linq.IOrderedEnumerable<object> System.Linq.Enumerable.OrderBy<object,byte>(System.Collections.Generic.IEnumerable<object>,System.Func<object,byte>)
		// System.Linq.IOrderedEnumerable<object> System.Linq.Enumerable.OrderBy<object,int>(System.Collections.Generic.IEnumerable<object>,System.Func<object,int>)
		// System.Linq.IOrderedEnumerable<object> System.Linq.Enumerable.OrderBy<object,object>(System.Collections.Generic.IEnumerable<object>,System.Func<object,object>,System.Collections.Generic.IComparer<object>)
		// System.Linq.IOrderedEnumerable<object> System.Linq.Enumerable.OrderByDescending<object,byte>(System.Collections.Generic.IEnumerable<object>,System.Func<object,byte>)
		// System.Collections.Generic.IEnumerable<object> System.Linq.Enumerable.Reverse<object>(System.Collections.Generic.IEnumerable<object>)
		// System.Collections.Generic.IEnumerable<object> System.Linq.Enumerable.ReverseIterator<object>(System.Collections.Generic.IEnumerable<object>)
		// System.Collections.Generic.IEnumerable<int> System.Linq.Enumerable.Select<$fK.$FK,int>(System.Collections.Generic.IEnumerable<$fK.$FK>,System.Func<$fK.$FK,int>)
		// System.Collections.Generic.IEnumerable<int> System.Linq.Enumerable.Select<object,int>(System.Collections.Generic.IEnumerable<object>,System.Func<object,int>)
		// System.Collections.Generic.IEnumerable<object> System.Linq.Enumerable.Skip<object>(System.Collections.Generic.IEnumerable<object>,int)
		// System.Collections.Generic.IEnumerable<object> System.Linq.Enumerable.SkipIterator<object>(System.Collections.Generic.IEnumerable<object>,int)
		// int System.Linq.Enumerable.Sum<$fK.$FK>(System.Collections.Generic.IEnumerable<$fK.$FK>,System.Func<$fK.$FK,int>)
		// System.Collections.Generic.IEnumerable<object> System.Linq.Enumerable.Take<object>(System.Collections.Generic.IEnumerable<object>,int)
		// System.Collections.Generic.IEnumerable<object> System.Linq.Enumerable.TakeIterator<object>(System.Collections.Generic.IEnumerable<object>,int)
		// System.Linq.IOrderedEnumerable<object> System.Linq.Enumerable.ThenBy<object,int>(System.Linq.IOrderedEnumerable<object>,System.Func<object,int>)
		// System.Linq.IOrderedEnumerable<object> System.Linq.Enumerable.ThenBy<object,uint>(System.Linq.IOrderedEnumerable<object>,System.Func<object,uint>)
		// System.Collections.Generic.KeyValuePair<int,object>[] System.Linq.Enumerable.ToArray<System.Collections.Generic.KeyValuePair<int,object>>(System.Collections.Generic.IEnumerable<System.Collections.Generic.KeyValuePair<int,object>>)
		// object[] System.Linq.Enumerable.ToArray<object>(System.Collections.Generic.IEnumerable<object>)
		// System.Collections.Generic.List<int> System.Linq.Enumerable.ToList<int>(System.Collections.Generic.IEnumerable<int>)
		// System.Collections.Generic.List<object> System.Linq.Enumerable.ToList<object>(System.Collections.Generic.IEnumerable<object>)
		// System.Collections.Generic.IEnumerable<object> System.Linq.Enumerable.Where<object>(System.Collections.Generic.IEnumerable<object>,System.Func<object,bool>)
		// System.Collections.Generic.IEnumerable<int> System.Linq.Enumerable.Iterator<$fK.$FK>.Select<int>(System.Func<$fK.$FK,int>)
		// System.Collections.Generic.IEnumerable<int> System.Linq.Enumerable.Iterator<object>.Select<int>(System.Func<object,int>)
		// System.Linq.IOrderedEnumerable<object> System.Linq.IOrderedEnumerable<object>.CreateOrderedEnumerable<int>(System.Func<object,int>,System.Collections.Generic.IComparer<int>,bool)
		// System.Linq.IOrderedEnumerable<object> System.Linq.IOrderedEnumerable<object>.CreateOrderedEnumerable<uint>(System.Func<object,uint>,System.Collections.Generic.IComparer<uint>,bool)
		// System.Void System.Runtime.CompilerServices.AsyncTaskMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$ec.$Fc>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$ec.$Fc&)
		// System.Void System.Runtime.CompilerServices.AsyncTaskMethodBuilder<System.Threading.Tasks.VoidTaskResult>.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$ec.$Fc>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$ec.$Fc&)
		// System.Void System.Runtime.CompilerServices.AsyncTaskMethodBuilder.Start<$ec.$Fc>($ec.$Fc&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$Bj.$ej>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$Bj.$ej&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$Dm.$em>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$Dm.$em&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$Fi.$gi>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$Fi.$gi&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$GI.$hI>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$GI.$hI&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$HF.$iF>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$HF.$iF&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$IN.$JN>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$IN.$JN&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$Jm.$lm>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$Jm.$lm&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$Kg.$Ng>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$Kg.$Ng&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$MN.$PN>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$MN.$PN&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$NF.$OF>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$NF.$OF&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$Qm.$Wm>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$Qm.$Wm&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$Qm.$Xm>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$Qm.$Xm&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$Qm.$xm>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$Qm.$xm&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$RB.$WB>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$RB.$WB&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$RB.$ZB>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$RB.$ZB&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$RB.$ac>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$RB.$ac&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$RB.$wB>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$RB.$wB&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$RB.$yB>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$RB.$yB&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$SG.$VG>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$SG.$VG&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$VM.$wM>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$VM.$wM&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$VN.$YN>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$VN.$YN&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$YH.$ZH>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$YH.$ZH&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$Yi.$aI>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$Yi.$aI&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$Zg.$CG>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$Zg.$CG&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$Zg.$DG>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$Zg.$DG&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$af.$Af>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$af.$Af&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$cI.$eI>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$cI.$eI&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$ch.$fh>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$ch.$fh&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$ci.$di>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$ci.$di&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$ec.$Gc>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$ec.$Gc&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$ec.$gc>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$ec.$gc&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$fo.$io>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$fo.$io&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$gL.$GL>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$gL.$GL&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$gk.$Ik>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$gk.$Ik&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$jI.$JI>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$jI.$JI&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$lE.$nE>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$lE.$nE&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$oI.$QI>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$oI.$QI&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$qM.$uM>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$qM.$uM&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$rE.$SE>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$rE.$SE&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$sh.$Uh>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$sh.$Uh&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$vA.$VA>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$vA.$VA&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$vh.$Vh>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$vh.$Vh&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$we.$Ye>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$we.$Ye&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,$xL.$yL>(Cysharp.Threading.Tasks.UniTask.Awaiter&,$xL.$yL&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,AutoRelease.$bb>(Cysharp.Threading.Tasks.UniTask.Awaiter&,AutoRelease.$bb&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,BulletEmitorLine.$ab>(Cysharp.Threading.Tasks.UniTask.Awaiter&,BulletEmitorLine.$ab&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,DropItem.$PA>(Cysharp.Threading.Tasks.UniTask.Awaiter&,DropItem.$PA&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,Monster.$SA>(Cysharp.Threading.Tasks.UniTask.Awaiter&,Monster.$SA&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,Monster.$sA>(Cysharp.Threading.Tasks.UniTask.Awaiter&,Monster.$sA&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,PVPHero.$De>(Cysharp.Threading.Tasks.UniTask.Awaiter&,PVPHero.$De&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,PVPPlayer.$ee>(Cysharp.Threading.Tasks.UniTask.Awaiter&,PVPPlayer.$ee&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,Player.$TA>(Cysharp.Threading.Tasks.UniTask.Awaiter&,Player.$TA&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,Player.$UA>(Cysharp.Threading.Tasks.UniTask.Awaiter&,Player.$UA&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<Cysharp.Threading.Tasks.UniTask.Awaiter,Player.$uA>(Cysharp.Threading.Tasks.UniTask.Awaiter&,Player.$uA&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<System.Runtime.CompilerServices.TaskAwaiter,$Cd.$Ed>(System.Runtime.CompilerServices.TaskAwaiter&,$Cd.$Ed&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<System.Runtime.CompilerServices.TaskAwaiter,$Ld.$Nd>(System.Runtime.CompilerServices.TaskAwaiter&,$Ld.$Nd&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<System.Runtime.CompilerServices.TaskAwaiter,$OC.$QC>(System.Runtime.CompilerServices.TaskAwaiter&,$OC.$QC&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<System.Runtime.CompilerServices.TaskAwaiter,$RB.$bc>(System.Runtime.CompilerServices.TaskAwaiter&,$RB.$bc&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<System.Runtime.CompilerServices.TaskAwaiter,$SJ.$ry>(System.Runtime.CompilerServices.TaskAwaiter&,$SJ.$ry&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<System.Runtime.CompilerServices.TaskAwaiter,$ec.$Hc>(System.Runtime.CompilerServices.TaskAwaiter&,$ec.$Hc&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<System.Runtime.CompilerServices.TaskAwaiter,$gC.$iC>(System.Runtime.CompilerServices.TaskAwaiter&,$gC.$iC&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<System.Runtime.CompilerServices.TaskAwaiter,$rd.$Sd>(System.Runtime.CompilerServices.TaskAwaiter&,$rd.$Sd&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<System.Runtime.CompilerServices.TaskAwaiter,$vd.$xd>(System.Runtime.CompilerServices.TaskAwaiter&,$vd.$xd&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.AwaitUnsafeOnCompleted<System.Runtime.CompilerServices.TaskAwaiter,LogWriter.$bO>(System.Runtime.CompilerServices.TaskAwaiter&,LogWriter.$bO&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$Bj.$ej>($Bj.$ej&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$Cd.$Ed>($Cd.$Ed&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$Dm.$em>($Dm.$em&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$Fi.$gi>($Fi.$gi&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$GI.$hI>($GI.$hI&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$HF.$iF>($HF.$iF&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$IN.$JN>($IN.$JN&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$Jm.$lm>($Jm.$lm&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$Kg.$Ng>($Kg.$Ng&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$Ld.$Nd>($Ld.$Nd&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$MN.$PN>($MN.$PN&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$NF.$OF>($NF.$OF&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$OC.$QC>($OC.$QC&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$Qm.$Wm>($Qm.$Wm&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$Qm.$Xm>($Qm.$Xm&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$Qm.$xm>($Qm.$xm&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$RB.$WB>($RB.$WB&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$RB.$ZB>($RB.$ZB&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$RB.$ac>($RB.$ac&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$RB.$bc>($RB.$bc&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$RB.$wB>($RB.$wB&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$RB.$yB>($RB.$yB&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$SG.$VG>($SG.$VG&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$SJ.$ry>($SJ.$ry&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$VM.$wM>($VM.$wM&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$VN.$YN>($VN.$YN&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$YH.$ZH>($YH.$ZH&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$Yi.$aI>($Yi.$aI&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$Zg.$CG>($Zg.$CG&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$Zg.$DG>($Zg.$DG&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$af.$Af>($af.$Af&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$cI.$eI>($cI.$eI&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$ch.$fh>($ch.$fh&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$ci.$di>($ci.$di&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$ec.$Gc>($ec.$Gc&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$ec.$Hc>($ec.$Hc&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$ec.$gc>($ec.$gc&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$fo.$io>($fo.$io&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$gC.$iC>($gC.$iC&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$gL.$GL>($gL.$GL&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$gk.$Ik>($gk.$Ik&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$jI.$JI>($jI.$JI&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$lE.$nE>($lE.$nE&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$oI.$QI>($oI.$QI&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$qM.$uM>($qM.$uM&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$rE.$SE>($rE.$SE&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$rd.$Sd>($rd.$Sd&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$sh.$Uh>($sh.$Uh&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$vA.$VA>($vA.$VA&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$vd.$xd>($vd.$xd&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$vh.$Vh>($vh.$Vh&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$we.$Ye>($we.$Ye&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<$xL.$yL>($xL.$yL&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<AutoRelease.$bb>(AutoRelease.$bb&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<BulletEmitorLine.$ab>(BulletEmitorLine.$ab&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<DropItem.$PA>(DropItem.$PA&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<LogWriter.$bO>(LogWriter.$bO&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<Monster.$SA>(Monster.$SA&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<Monster.$sA>(Monster.$sA&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<PVPHero.$De>(PVPHero.$De&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<PVPPlayer.$ee>(PVPPlayer.$ee&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<Player.$TA>(Player.$TA&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<Player.$UA>(Player.$UA&)
		// System.Void System.Runtime.CompilerServices.AsyncVoidMethodBuilder.Start<Player.$uA>(Player.$uA&)
		// object& System.Runtime.CompilerServices.Unsafe.As<object,object>(object&)
		// System.Void* System.Runtime.CompilerServices.Unsafe.AsPointer<object>(object&)
		// object UnityEngine.AndroidJNIHelper.ConvertFromJNIArray<object>(System.IntPtr)
		// System.IntPtr UnityEngine.AndroidJNIHelper.GetFieldID<object>(System.IntPtr,string,bool)
		// System.IntPtr UnityEngine.AndroidJNIHelper.GetMethodID<object>(System.IntPtr,string,object[],bool)
		// object UnityEngine.AndroidJavaObject.Call<object>(string,object[])
		// object UnityEngine.AndroidJavaObject.FromJavaArrayDeleteLocalRef<object>(System.IntPtr)
		// object UnityEngine.AndroidJavaObject.GetStatic<object>(string)
		// object UnityEngine.AndroidJavaObject._Call<object>(string,object[])
		// object UnityEngine.AndroidJavaObject._GetStatic<object>(string)
		// object UnityEngine.AssetBundle.LoadAsset<object>(string)
		// object UnityEngine.Component.GetComponent<object>()
		// object UnityEngine.Component.GetComponentInChildren<object>()
		// object UnityEngine.Component.GetComponentInParent<object>()
		// object[] UnityEngine.Component.GetComponentsInChildren<object>()
		// object[] UnityEngine.Component.GetComponentsInChildren<object>(bool)
		// bool UnityEngine.Component.TryGetComponent<object>(object&)
		// object UnityEngine.GameObject.AddComponent<object>()
		// object UnityEngine.GameObject.GetComponent<object>()
		// object UnityEngine.GameObject.GetComponentInChildren<object>()
		// object UnityEngine.GameObject.GetComponentInChildren<object>(bool)
		// System.Void UnityEngine.GameObject.GetComponentsInChildren<object>(bool,System.Collections.Generic.List<object>)
		// object[] UnityEngine.GameObject.GetComponentsInChildren<object>()
		// object[] UnityEngine.GameObject.GetComponentsInChildren<object>(bool)
		// bool UnityEngine.GameObject.TryGetComponent<object>(object&)
		// object UnityEngine.Object.FindObjectOfType<object>()
		// object[] UnityEngine.Object.FindObjectsOfType<object>()
		// object[] UnityEngine.Object.FindObjectsOfType<object>(bool)
		// object UnityEngine.Object.Instantiate<object>(object)
		// object UnityEngine.Object.Instantiate<object>(object,UnityEngine.Transform)
		// object UnityEngine.Object.Instantiate<object>(object,UnityEngine.Transform,bool)
		// object UnityEngine.Object.Instantiate<object>(object,UnityEngine.Vector3,UnityEngine.Quaternion,UnityEngine.Transform)
		// object[] UnityEngine.Resources.ConvertObjects<object>(UnityEngine.Object[])
		// object UnityEngine._AndroidJNIHelper.ConvertFromJNIArray<object>(System.IntPtr)
		// System.IntPtr UnityEngine._AndroidJNIHelper.GetFieldID<object>(System.IntPtr,string,bool)
		// System.IntPtr UnityEngine._AndroidJNIHelper.GetMethodID<object>(System.IntPtr,string,object[],bool)
		// string UnityEngine._AndroidJNIHelper.GetSignature<object>(object[])
		// YooAsset.AssetOperationHandle YooAsset.ResourcePackage.LoadAssetAsync<object>(string)
		// YooAsset.AssetOperationHandle YooAsset.ResourcePackage.LoadAssetSync<object>(string)
		// YooAsset.AssetOperationHandle YooAsset.YooAssets.LoadAssetSync<object>(string)
	}
}